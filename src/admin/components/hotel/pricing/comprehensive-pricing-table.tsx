import React, {
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  Button,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Input,
  Label,
  Select,
  Prompt,
} from "@camped-ai/ui";
import { Save, Loader2, Calendar, Copy, Download, Upload } from "lucide-react";
// Import export modal
import PricingExportModal from "./pricing-export-modal";
// Import bulk import modal
import BulkImportModal from "./bulk-import-modal";
// Removed useAdminHotelPricing import - using direct fetch to avoid duplicate API calls
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
// import { CurrencySelector } from "../../common/currency-selector";
// Import cost-margin calculator utilities
import { calculateTotalFromCostMargin } from "../../../../modules/hotel-management/hotel-pricing/utils/cost-margin-calculator";

import { format } from "date-fns";
import BulkPriceUpdateModal from "./bulk-price-update-modal";
import HotelPricingTableSkeleton from "../../shared/hotel-pricing-table-skeleton";

// Import types
import type {
  PricingRow,
  ComprehensivePricingTableProps,
  WeekdayInfo,
} from "./types";

// Helper function to generate a deterministic unique ID based on row properties
const generateRowId = (
  roomConfigId: string,
  occupancyTypeId: string,
  mealPlanId: string | null,
  seasonalPeriodId?: string
) => {
  const mealPlanPart = mealPlanId || "no-meal";
  const seasonalPart = seasonalPeriodId || "base";
  return `${roomConfigId}-${occupancyTypeId}-${mealPlanPart}-${seasonalPart}`;
};

// Helper function to validate and format numeric input
const validateNumericInput = (
  value: string,
  isPercentage: boolean = false
): string => {
  // Remove any non-numeric characters except decimal point, negative sign, and % for percentages
  let cleaned = value.replace(/[^0-9.-]/g, "");

  // Handle multiple decimal points - keep only the first one
  const decimalIndex = cleaned.indexOf(".");
  if (decimalIndex !== -1) {
    cleaned =
      cleaned.substring(0, decimalIndex + 1) +
      cleaned.substring(decimalIndex + 1).replace(/\./g, "");
  }

  // Handle multiple negative signs - keep only the first one if it's at the beginning
  if (cleaned.includes("-")) {
    const firstChar = cleaned.charAt(0);
    cleaned = cleaned.replace(/-/g, "");
    if (firstChar === "-") {
      cleaned = "-" + cleaned;
    }
  }

  return cleaned;
};

// Helper function to handle key press validation for numeric inputs
const handleNumericKeyPress = (
  e: React.KeyboardEvent<HTMLInputElement>,
  isPercentage: boolean = false
) => {
  const allowedKeys = [
    "Backspace",
    "Delete",
    "Tab",
    "Escape",
    "Enter",
    "ArrowLeft",
    "ArrowRight",
    "ArrowUp",
    "ArrowDown",
    "Home",
    "End",
  ];

  // Allow control keys
  if (allowedKeys.includes(e.key) || e.ctrlKey || e.metaKey) {
    return;
  }

  const currentValue = (e.target as HTMLInputElement).value;
  const key = e.key;

  // Allow digits
  if (/[0-9]/.test(key)) {
    return;
  }

  // Allow decimal point if not already present
  if (key === "." && !currentValue.includes(".")) {
    return;
  }

  // Allow negative sign only at the beginning and if not already present
  if (key === "-" && currentValue.length === 0) {
    return;
  }

  // Prevent all other characters
  e.preventDefault();
};

// Debounced input component to improve performance
const DebouncedInput = React.memo(
  ({
    value,
    onChange,
    onKeyDown,
    className,
    placeholder,
    type = "text",
    debounceMs = 300,
  }: {
    value: string;
    onChange: (value: string) => void;
    onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
    className?: string;
    placeholder?: string;
    type?: string;
    debounceMs?: number;
  }) => {
    const [localValue, setLocalValue] = useState(value);
    const timeoutRef = useRef<NodeJS.Timeout>();

    // Update local value when prop value changes
    useEffect(() => {
      setLocalValue(value);
    }, [value]);

    // Debounced onChange handler
    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setLocalValue(newValue);

        // Clear existing timeout
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        // Set new timeout
        timeoutRef.current = setTimeout(() => {
          onChange(newValue);
        }, debounceMs);
      },
      [onChange, debounceMs]
    );

    // Cleanup timeout on unmount
    useEffect(() => {
      return () => {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }
      };
    }, []);

    return (
      <Input
        type={type}
        value={localValue}
        onChange={handleChange}
        onKeyDown={onKeyDown}
        className={className}
        placeholder={placeholder}
      />
    );
  }
);

const ComprehensivePricingTable: React.FC<ComprehensivePricingTableProps> = ({
  hotelId: _hotelId, // Prefix with underscore to indicate intentionally unused
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods,
  setSeasonalPeriods,
  initialPrices = {},
  roomPricingData = [],
  onSave,
  canEdit = false,
  canCreate = false,
  canDelete = false,
  hideBackButton = false,
  readOnlyMode = false, // New prop for read-only variant
  currentCurrency: propCurrentCurrency,
  onCurrencyChange,
  onRefetch,
}) => {
  const [pricingRows, setPricingRows] = useState<PricingRow[]>([]);
  const [currencyCode, setCurrencyCode] = useState(
    propCurrentCurrency || "GBP"
  );
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedRoomConfig, setSelectedRoomConfig] = useState<string>("");
  const [selectedOccupancyConfig, setSelectedOccupancyConfig] =
    useState<string>("");
  const [selectedMealPlan, setSelectedMealPlan] = useState<string>("");
  const [selectedSeasonFilter, setSelectedSeasonFilter] =
    useState<string>("all");
  const [showAllDays] = useState(true); // Always show all days for now

  // Export modal state
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);

  // Bulk import modal state
  const [isBulkImportModalOpen, setIsBulkImportModalOpen] = useState(false);

  const {
    currencies,
    defaultCurrency,
    isLoading: isLoadingCurrencies,
  } = useAdminCurrencies();

  // Get current currency object for proper formatting
  const currentCurrency =
    currencies.find((c: any) => c.currency_code === currencyCode) ||
    defaultCurrency;

  // Helper function to format price display
  const formatPrice = (amount: number | null | undefined): string => {
    if (!amount) return "";

    // Format to 2 decimal places and remove trailing zeros
    const formatted = Number(amount).toFixed(2);
    // Remove trailing zeros and decimal point if not needed
    return formatted.replace(/\.?0+$/, '');
  };

  console.log({ currentCurrency, currencyCode });

  // Helper function to get currency symbol
  const getCurrencySymbol = (): string => {
    return currentCurrency?.symbol || currencyCode;
  };

  // Helper function to safely convert currency values from database to display format
  // Handle inconsistent API responses - some return cents, some return display units
  const convertCurrencyValue = (
    value: string | number | null | undefined
  ): number => {
    if (!value) return 0;

    // Convert string to number if needed
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (typeof numValue !== "number" || isNaN(numValue)) return 0;

    // Smart detection logic:
    // 1. Values >= 100 are likely in cents (e.g., 470 cents = 4.70 display)
    // 2. Values < 100 are likely already in display units (e.g., 4.7 display)
    // 3. String values are more likely to be in cents (legacy format)

    let needsConversion = false;

    if (typeof value === "string") {
      // String values are typically in cents
      needsConversion = true;
    } else {
      // For numeric values, use threshold-based detection
      // Values >= 100 are likely in cents, < 100 are likely display units
      needsConversion = numValue >= 100;
    }

    const result = needsConversion ? numValue / 100 : numValue;

    console.log(
      `💰 convertCurrencyValue: ${value} (${typeof value}) -> ${
        needsConversion ? "CONVERTED" : "KEPT"
      }: ${result}`
    );

    return result;
  };

  // Helper function to safely convert percentage values from database to display format
  const convertPercentageValue = (
    value: string | number | null | undefined
  ): number => {
    if (!value) return 0;

    // Convert string to number if needed
    const numValue = typeof value === "string" ? parseFloat(value) : value;
    if (typeof numValue !== "number" || isNaN(numValue)) return 0;

    // If the value is > 100, it's likely stored as basis points (e.g., 1000 = 10%)
    // If the value is <= 100, it's likely stored as percentage (e.g., 10 = 10%)
    const result = numValue > 100 ? numValue / 100 : numValue;
    return typeof result === "number" && !isNaN(result) ? result : 0;
  };

  // Helper function to parse user input (display format) to component state format
  const parsePrice = (displayValue: string): number => {
    // Parse the display value and return it as-is for component state
    // The component state stores values in display format (dollars)
    return parseFloat(displayValue) || 0;
  };

  // Helper function to get available meal plans for an occupancy type
  const getAvailableMealPlans = (occupancyTypeId: string) => {
    const occupancy = occupancyConfigs.find((oc) => oc.id === occupancyTypeId);
    if (!occupancy) return mealPlans;

    // Legacy logic for special accommodations (backward compatibility)
    const isExtraBed =
      (occupancy as any).type === "EXTRA_BED" ||
      occupancy.name?.toLowerCase().includes("extra bed");
    const isCot =
      (occupancy as any).type === "COT" ||
      occupancy.name?.toLowerCase().includes("cot");

    if (isExtraBed || isCot) {
      return [{ id: null, name: "N/A" }];
    }

    // Filter meal plans based on their applicable_occupancy_types metadata
    const availableMealPlans = mealPlans.filter((mealPlan) => {
      const applicableTypes = mealPlan.metadata?.applicable_occupancy_types;

      // If no applicable types specified, meal plan is available for all occupancy types
      if (!applicableTypes || applicableTypes.length === 0) {
        return true;
      }

      // Check if this occupancy type is in the applicable types
      return applicableTypes.includes(occupancyTypeId);
    });

    // If no meal plans are available, show N/A
    if (availableMealPlans.length === 0) {
      return [{ id: null, name: "N/A" }];
    }

    return availableMealPlans;
  };

  // State for bulk price update modal
  const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = useState(false);

  // Initialize dates with noon time to avoid timezone issues
  const today = new Date();
  today.setHours(12, 0, 0, 0);

  const nextWeek = new Date(today);
  nextWeek.setDate(today.getDate() + 7);

  // State for apply to all days confirmation prompt
  const [isApplyToAllDaysPromptOpen, setIsApplyToAllDaysPromptOpen] =
    useState(false);
  const [pendingApplyToAllDaysData, setPendingApplyToAllDaysData] = useState<{
    roomConfigId: string;
    occupancyTypeId: string;
    mealPlanId: string | null;
    seasonalPeriodId: string | undefined;
    pricingRow: PricingRow;
  } | null>(null);

  // Removed savePricing hook to avoid duplicate API calls - using direct fetch instead

  const weekdays: WeekdayInfo[] = [
    { id: "mon", name: "Monday" },
    { id: "tue", name: "Tuesday" },
    { id: "wed", name: "Wednesday" },
    { id: "thu", name: "Thursday" },
    { id: "fri", name: "Friday" },
    { id: "sat", name: "Saturday" },
    { id: "sun", name: "Sunday" },
  ];

  // Set default currency when currencies are loaded
  useEffect(() => {
    if (defaultCurrency && !currencyCode) {
      setCurrencyCode(defaultCurrency.currency_code);
    }
  }, [defaultCurrency, currencyCode]);

  // Initialize data when components load AND currency is set
  useEffect(() => {
    if (!isLoadingCurrencies && currencyCode) {
      initializeData();
    }
  }, [
    initialPrices,
    roomPricingData,
    roomConfigs,
    occupancyConfigs,
    mealPlans,
    seasonalPeriods,
    isLoadingCurrencies,
    currencyCode, // Add currencyCode as dependency
  ]);

  const initializeData = (targetCurrency?: string) => {
    const activeCurrency = targetCurrency || currencyCode;

    // Removed currency conversion test call

    // Don't initialize if no currency is set
    if (!activeCurrency) {
      return;
    }

    setIsLoading(true);

    const newRows: PricingRow[] = [];

    // For each room config, create pricing rows for all combinations
    roomConfigs.forEach((room) => {
      // Get pricing data for this room - try new structure first, then fall back to legacy
      let currencyFilteredPrices: any = {
        weekday_rules: [],
        seasonal_prices: [],
      };

      // Method 1: Try new comprehensive API structure (roomPricingData)
      if (roomPricingData && roomPricingData.length > 0) {
        const roomData = roomPricingData.find(
          (data) => data.room_config_id === room.id
        );
        if (roomData) {
          // Filter weekday rules by currency
          const weekdayRules = roomData.weekday_rules.filter(
            (rule) =>
              rule.currency_code === activeCurrency || !rule.currency_code
          );

          // Filter seasonal prices by currency
          const seasonalPrices = roomData.seasonal_prices.filter(
            (price) =>
              price.currency_code === activeCurrency || !price.currency_code
          );

          currencyFilteredPrices = {
            weekday_rules: weekdayRules,
            seasonal_prices: seasonalPrices,
          };
        }
      }

      // Method 2: Fall back to legacy initialPrices structure
      if (
        currencyFilteredPrices.weekday_rules.length === 0 &&
        initialPrices[room.id]
      ) {
        const roomPrices = initialPrices[room.id] || {};

        if (roomPrices[activeCurrency]) {
          // Multi-currency structure - look for currency-specific data
          console.log(
            `Room ${room.id} - Found ${activeCurrency} data in legacy structure:`,
            roomPrices[activeCurrency]
          );
          currencyFilteredPrices = roomPrices[activeCurrency];
        } else if (roomPrices.currency_code === activeCurrency) {
          // Legacy single currency structure - direct match
          console.log(
            `Room ${room.id} - Using legacy single currency data for ${activeCurrency}`
          );
          currencyFilteredPrices = roomPrices;
        }
      }

      // Create base pricing rows (no seasonal period)
      occupancyConfigs.forEach((occupancy) => {
        // Get available meal plans for this occupancy type
        const mealPlansToProcess = getAvailableMealPlans(occupancy.id);

        mealPlansToProcess.forEach((mealPlan) => {
          // Check if we have a price for this combination
          const existingRule = currencyFilteredPrices.weekday_rules?.find(
            (rule: any) =>
              rule.occupancy_type_id === occupancy.id &&
              (mealPlan.id === null
                ? !rule.meal_plan_id || rule.meal_plan_id === null
                : rule.meal_plan_id === mealPlan.id)
          );

          // Debug logging for weekday prices
          if (existingRule?.weekday_prices) {
            console.log(
              `🔍 DEBUG: Found existing rule for ${room.id}-${occupancy.id}-${mealPlan.id}:`,
              {
                weekday_prices: existingRule.weekday_prices,
                default_values: existingRule.default_values,
                weekday_values: existingRule.weekday_values,
              }
            );
          }

          // Create pricing row with weekday prices
          newRows.push({
            id: generateRowId(room.id, occupancy.id, mealPlan.id),
            roomConfigId: room.id,
            occupancyTypeId: occupancy.id,
            mealPlanId: mealPlan.id,
            seasonalPeriodId: undefined, // Base pricing
            prices: {
              mon: convertCurrencyValue(existingRule?.weekday_prices?.mon),
              tue: convertCurrencyValue(existingRule?.weekday_prices?.tue),
              wed: convertCurrencyValue(existingRule?.weekday_prices?.wed),
              thu: convertCurrencyValue(existingRule?.weekday_prices?.thu),
              fri: convertCurrencyValue(existingRule?.weekday_prices?.fri),
              sat: convertCurrencyValue(existingRule?.weekday_prices?.sat),
              sun: convertCurrencyValue(existingRule?.weekday_prices?.sun),
            },
            // Initialize default cost and margin values
            defaultValues: {
              grossCost: convertCurrencyValue(
                existingRule?.default_values?.gross_cost
              ),
              fixedMargin: convertCurrencyValue(
                existingRule?.default_values?.fixed_margin
              ),
              marginPercentage: convertPercentageValue(
                existingRule?.default_values?.margin_percentage
              ),
              total: convertCurrencyValue(existingRule?.default_values?.total),
            },
            // Initialize weekday-specific cost and margin values
            // Use new weekday_values structure if available, fallback to cost_margin_data or legacy fields
            weekdayValues: {
              mon: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.mon?.gross_cost ||
                    existingRule?.cost_margin_data?.mon?.gross_cost ||
                    existingRule?.monday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.mon?.fixed_margin ||
                    existingRule?.cost_margin_data?.mon?.fixed_margin ||
                    existingRule?.monday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.mon?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.mon?.margin_percentage;
                  const directValue = existingRule?.monday_margin_percentage;

                  // console.log(
                  //   `[FRONTEND] Margin percentage values for Monday:`,
                  //   {
                  //     existingRule,
                  //     weekdayValue,
                  //     costMarginValue,
                  //     directValue,
                  //   }
                  // );

                  // Use the most specific weekday data available, or empty if none
                  const actualValue =
                    weekdayValue ?? costMarginValue ?? directValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.monday_price),
              },
              tue: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.tue?.gross_cost ||
                    existingRule?.cost_margin_data?.tue?.gross_cost ||
                    existingRule?.tuesday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.tue?.fixed_margin ||
                    existingRule?.cost_margin_data?.tue?.fixed_margin ||
                    existingRule?.tuesday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.tue?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.tue?.margin_percentage;
                  const directValue = existingRule?.tuesday_margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue =
                    weekdayValue ?? costMarginValue ?? directValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.tuesday_price),
              },
              wed: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.wed?.gross_cost ||
                    existingRule?.cost_margin_data?.wed?.gross_cost ||
                    existingRule?.wednesday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.wed?.fixed_margin ||
                    existingRule?.cost_margin_data?.wed?.fixed_margin ||
                    existingRule?.wednesday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.wed?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.wed?.margin_percentage;
                  const directValue = existingRule?.wednesday_margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue =
                    weekdayValue ?? costMarginValue ?? directValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.wednesday_price),
              },
              thu: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.thu?.gross_cost ||
                    existingRule?.cost_margin_data?.thu?.gross_cost ||
                    existingRule?.thursday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.thu?.fixed_margin ||
                    existingRule?.cost_margin_data?.thu?.fixed_margin ||
                    existingRule?.thursday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.thu?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.thu?.margin_percentage;
                  const directValue = existingRule?.thursday_margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue =
                    weekdayValue ?? costMarginValue ?? directValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.thursday_price),
              },
              fri: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.fri?.gross_cost ||
                    existingRule?.cost_margin_data?.fri?.gross_cost ||
                    existingRule?.friday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.fri?.fixed_margin ||
                    existingRule?.cost_margin_data?.fri?.fixed_margin ||
                    existingRule?.friday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.fri?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.fri?.margin_percentage;
                  const directValue = existingRule?.friday_margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue =
                    weekdayValue ?? costMarginValue ?? directValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.friday_price),
              },
              sat: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.sat?.gross_cost ||
                    existingRule?.cost_margin_data?.sat?.gross_cost ||
                    existingRule?.saturday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.sat?.fixed_margin ||
                    existingRule?.cost_margin_data?.sat?.fixed_margin ||
                    existingRule?.saturday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.sat?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.sat?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.saturday_price),
              },
              sun: {
                grossCost: convertCurrencyValue(
                  existingRule?.weekday_values?.sun?.gross_cost ||
                    existingRule?.cost_margin_data?.sun?.gross_cost ||
                    existingRule?.sunday_gross_cost
                ),
                fixedMargin: convertCurrencyValue(
                  existingRule?.weekday_values?.sun?.fixed_margin ||
                    existingRule?.cost_margin_data?.sun?.fixed_margin ||
                    existingRule?.sunday_fixed_margin
                ),
                marginPercentage: (() => {
                  // Show exactly what we have for this specific weekday - no fallbacks to defaults
                  const weekdayValue =
                    existingRule?.weekday_values?.sun?.margin_percentage;
                  const costMarginValue =
                    existingRule?.cost_margin_data?.sun?.margin_percentage;

                  // Use the most specific weekday data available, or empty if none
                  const actualValue = weekdayValue ?? costMarginValue;

                  return actualValue !== undefined && actualValue !== null
                    ? convertPercentageValue(actualValue)
                    : 0; // Empty if no weekday-specific data
                })(),
                total: convertCurrencyValue(existingRule?.sunday_price),
              },
            },
            modified: false,
          });

          // Create seasonal pricing rows
          seasonalPeriods.forEach((season) => {
            // Check if we have seasonal prices for this combination
            const seasonalPrices = currencyFilteredPrices.seasonal_prices || [];
            const existingSeasonalPrice = seasonalPrices.find(
              (sp: any) => sp.id === season.id || sp.name === season.name
            );

            const existingSeasonalRule =
              existingSeasonalPrice?.weekday_rules?.find(
                (rule: any) =>
                  rule.occupancy_type_id === occupancy.id &&
                  (mealPlan.id === null
                    ? !rule.meal_plan_id || rule.meal_plan_id === null
                    : rule.meal_plan_id === mealPlan.id)
              );

            // Create pricing row with weekday prices for this season
            newRows.push({
              id: generateRowId(room.id, occupancy.id, mealPlan.id, season.id),
              roomConfigId: room.id,
              occupancyTypeId: occupancy.id,
              mealPlanId: mealPlan.id,
              seasonalPeriodId: season.id,
              prices: {
                mon: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.weekday_prices?.mon
                    )
                  : 0,
                tue: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.weekday_prices?.tue
                    )
                  : 0,
                wed: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.weekday_prices?.wed
                    )
                  : 0,
                thu: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.weekday_prices?.thu
                    )
                  : 0,
                fri: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.weekday_prices?.fri
                    )
                  : 0,
                sat: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.weekday_prices?.sat
                    )
                  : 0,
                sun: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.weekday_prices?.sun
                    )
                  : 0,
              },
              // Enhanced: Read default cost and margin values from seasonal pricing API response
              defaultValues: {
                grossCost: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.default_values?.gross_cost
                    )
                  : 0,
                fixedMargin: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.default_values?.fixed_margin
                    )
                  : 0,
                marginPercentage: existingSeasonalRule
                  ? convertPercentageValue(
                      existingSeasonalRule.default_values?.margin_percentage
                    )
                  : 0,
                total: existingSeasonalRule
                  ? convertCurrencyValue(
                      existingSeasonalRule.default_values?.total
                    )
                  : 0,
              },
              // Enhanced: Read weekday-specific cost and margin values from seasonal pricing API response
              weekdayValues: {
                mon: {
                  grossCost: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.mon?.gross_cost
                      )
                    : 0,
                  fixedMargin: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.mon?.fixed_margin
                      )
                    : 0,
                  marginPercentage: existingSeasonalRule
                    ? convertPercentageValue(
                        existingSeasonalRule.weekday_values?.mon
                          ?.margin_percentage
                      )
                    : 0,
                  total: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_prices?.mon
                      )
                    : 0,
                },
                tue: {
                  grossCost: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.tue?.gross_cost
                      )
                    : 0,
                  fixedMargin: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.tue?.fixed_margin
                      )
                    : 0,
                  marginPercentage: existingSeasonalRule
                    ? convertPercentageValue(
                        existingSeasonalRule.weekday_values?.tue
                          ?.margin_percentage
                      )
                    : 0,
                  total: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_prices?.tue
                      )
                    : 0,
                },
                wed: {
                  grossCost: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.wed?.gross_cost
                      )
                    : 0,
                  fixedMargin: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.wed?.fixed_margin
                      )
                    : 0,
                  marginPercentage: existingSeasonalRule
                    ? convertPercentageValue(
                        existingSeasonalRule.weekday_values?.wed
                          ?.margin_percentage
                      )
                    : 0,
                  total: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_prices?.wed
                      )
                    : 0,
                },
                thu: {
                  grossCost: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.thu?.gross_cost
                      )
                    : 0,
                  fixedMargin: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.thu?.fixed_margin
                      )
                    : 0,
                  marginPercentage: existingSeasonalRule
                    ? convertPercentageValue(
                        existingSeasonalRule.weekday_values?.thu
                          ?.margin_percentage
                      )
                    : 0,
                  total: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_prices?.thu
                      )
                    : 0,
                },
                fri: {
                  grossCost: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.fri?.gross_cost
                      )
                    : 0,
                  fixedMargin: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.fri?.fixed_margin
                      )
                    : 0,
                  marginPercentage: existingSeasonalRule
                    ? convertPercentageValue(
                        existingSeasonalRule.weekday_values?.fri
                          ?.margin_percentage
                      )
                    : 0,
                  total: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_prices?.fri
                      )
                    : 0,
                },
                sat: {
                  grossCost: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.sat?.gross_cost
                      )
                    : 0,
                  fixedMargin: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.sat?.fixed_margin
                      )
                    : 0,
                  marginPercentage: existingSeasonalRule
                    ? convertPercentageValue(
                        existingSeasonalRule.weekday_values?.sat
                          ?.margin_percentage
                      )
                    : 0,
                  total: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_prices?.sat
                      )
                    : 0,
                },
                sun: {
                  grossCost: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.sun?.gross_cost
                      )
                    : 0,
                  fixedMargin: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_values?.sun?.fixed_margin
                      )
                    : 0,
                  marginPercentage: existingSeasonalRule
                    ? convertPercentageValue(
                        existingSeasonalRule.weekday_values?.sun
                          ?.margin_percentage
                      )
                    : 0,
                  total: existingSeasonalRule
                    ? convertCurrencyValue(
                        existingSeasonalRule.weekday_prices?.sun
                      )
                    : 0,
                },
              },
              modified: false,
            });
          });
        });
      });
    });

    setPricingRows(newRows);
    setIsLoading(false);
  };

  const handlePriceChange = useCallback(
    (
      roomConfigId: string,
      occupancyTypeId: string,
      mealPlanId: string | null,
      seasonalPeriodId: string | undefined,
      day: string,
      value: number
    ) => {
      setPricingRows((prev) =>
        prev.map((row) =>
          row.roomConfigId === roomConfigId &&
          row.occupancyTypeId === occupancyTypeId &&
          row.mealPlanId === mealPlanId &&
          row.seasonalPeriodId === seasonalPeriodId
            ? {
                ...row,
                prices: { ...row.prices, [day]: value },
                modified: true,
              }
            : row
        )
      );
    },
    []
  );

  const handleCurrencyChange = (newCurrencyCode: string) => {
    console.log(
      `Currency change requested: ${currencyCode} → ${newCurrencyCode}`
    );
    setCurrencyCode(newCurrencyCode);

    // If parent provides currency change handler, use it (this will trigger data refetch)
    if (onCurrencyChange) {
      onCurrencyChange(newCurrencyCode);
    } else {
      // Fallback: Re-initialize data with the new currency to filter pricing data
      // Pass the new currency directly to avoid state timing issues
      initializeData(newCurrencyCode);
    }
  };

  const handleCopyBaseToSeasonal = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null
  ) => {
    // Find the base pricing row
    const basePricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        !row.seasonalPeriodId
    );

    if (!basePricingRow) return;

    // Update all seasonal pricing rows for this combination
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId // Only seasonal rows
          ? {
              ...row,
              prices: { ...basePricingRow.prices },
              modified: true,
            }
          : row
      )
    );

    toast.success("Success", {
      description: "Base prices copied to all seasonal periods",
    });
  };

  console.log({ pricingRows });

  const handleCopyToAllDays = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    sourceDay: string
  ) => {
    // Find the pricing row
    const pricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
    );

    if (!pricingRow) return;

    // Get the price from the source day
    const sourcePrice =
      pricingRow.prices[sourceDay as keyof typeof pricingRow.prices];

    // Create new prices with the same value for all days
    const newPrices = {
      mon: sourcePrice,
      tue: sourcePrice,
      wed: sourcePrice,
      thu: sourcePrice,
      fri: sourcePrice,
      sat: sourcePrice,
      sun: sourcePrice,
    };

    // Update the pricing row
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
          ? {
              ...row,
              prices: newPrices,
              modified: true,
            }
          : row
      )
    );

    toast.success("Success", {
      description: `${sourceDay.toUpperCase()} price copied to all days`,
    });
  };

  const handleBulkPriceUpdate = (updatedRows: PricingRow[]) => {
    console.log("🔄 [BULK UPDATE] Starting bulk update process");
    console.log("🔄 [BULK UPDATE] Original rows count:", pricingRows.length);
    console.log("🔄 [BULK UPDATE] Updated rows count:", updatedRows.length);

    // Log sample of changes for debugging
    const sampleChanges = updatedRows.slice(0, 3).map((row, index) => {
      const originalRow = pricingRows.find(
        (r) =>
          r.roomConfigId === row.roomConfigId &&
          r.occupancyTypeId === row.occupancyTypeId &&
          r.mealPlanId === row.mealPlanId &&
          r.seasonalPeriodId === row.seasonalPeriodId
      );

      return {
        rowIndex: index,
        roomConfigId: row.roomConfigId,
        occupancyTypeId: row.occupancyTypeId,
        mealPlanId: row.mealPlanId,
        seasonalPeriodId: row.seasonalPeriodId,
        original_prices: originalRow?.prices,
        updated_prices: row.prices,
        price_changes: {
          mon: `${originalRow?.prices.mon || 0} → ${row.prices.mon}`,
          tue: `${originalRow?.prices.tue || 0} → ${row.prices.tue}`,
          wed: `${originalRow?.prices.wed || 0} → ${row.prices.wed}`,
        },
        modified: row.modified,
      };
    });

    console.log("🔄 [BULK UPDATE] Sample price changes:", sampleChanges);
    console.log("🔄 [BULK UPDATE] Applying bulk update to state");

    setPricingRows(updatedRows);
    toast.success("Success", {
      description: "Bulk price update applied successfully",
    });
  };

  // Handle changes to default cost and margin values with REAL-TIME CALCULATION
  const handleDefaultValueChange = useCallback(
    (
      roomConfigId: string,
      occupancyTypeId: string,
      mealPlanId: string | null,
      seasonalPeriodId: string | undefined,
      field: "grossCost" | "fixedMargin" | "marginPercentage",
      value: number
    ) => {
      console.log(`📝 Default value change: ${field} = ${value}`);

      setPricingRows((prev) =>
        prev.map((row) => {
          if (
            row.roomConfigId === roomConfigId &&
            row.occupancyTypeId === occupancyTypeId &&
            row.mealPlanId === mealPlanId &&
            row.seasonalPeriodId === seasonalPeriodId
          ) {
            // Update the specific field first
            const updatedDefaultValues = {
              ...row.defaultValues,
              [field]: value,
            };

            // MUTUAL EXCLUSION LOGIC: Clear the opposite margin field when one is entered
            if (field === "fixedMargin" && value > 0) {
              // When Fixed Margin is entered, clear Margin Percentage for defaults
              updatedDefaultValues.marginPercentage = 0;
              console.log(
                `🔄 Mutual exclusion: Cleared default margin percentage because fixed margin was set to ${value}`
              );
            } else if (field === "marginPercentage" && value > 0) {
              // When Margin Percentage is entered, clear Fixed Margin for defaults
              updatedDefaultValues.fixedMargin = 0;
              console.log(
                `🔄 Mutual exclusion: Cleared default fixed margin because margin percentage was set to ${value}`
              );
            }

            // Calculate the new total automatically using the cost-margin calculator
            const calculatedTotal = calculateTotalFromCostMargin({
              gross_cost: updatedDefaultValues.grossCost, // Backend handles conversion
              fixed_margin: updatedDefaultValues.fixedMargin, // Backend handles conversion
              margin_percentage: updatedDefaultValues.marginPercentage, // Percentage stays as-is
            });

            // Update the total field with calculated value
            if (calculatedTotal !== null && calculatedTotal !== undefined) {
              updatedDefaultValues.total = calculatedTotal; // Use calculated value directly
              console.log(
                `🧮 Auto-calculated default total: cost=${updatedDefaultValues.grossCost}, margin=${updatedDefaultValues.fixedMargin}, %=${updatedDefaultValues.marginPercentage} → total=${updatedDefaultValues.total}`
              );
            } else {
              // If calculation fails, keep existing total
              console.log(
                `⚠️ Calculation failed, keeping existing total: ${updatedDefaultValues.total}`
              );
            }

            return {
              ...row,
              defaultValues: updatedDefaultValues,
              modified: true,
            };
          }
          return row;
        })
      );
    },
    []
  );

  // Handle changes to weekday-specific cost and margin values with automatic calculation
  const handleWeekdayValueChange = useCallback(
    (
      roomConfigId: string,
      occupancyTypeId: string,
      mealPlanId: string | null,
      seasonalPeriodId: string | undefined,
      day: "mon" | "tue" | "wed" | "thu" | "fri" | "sat" | "sun",
      field: "grossCost" | "fixedMargin" | "marginPercentage",
      value: number
    ) => {
      console.log(`📝 Weekday value change: ${day} ${field} = ${value}`);

      setPricingRows((prev) =>
        prev.map((row) => {
          if (
            row.roomConfigId === roomConfigId &&
            row.occupancyTypeId === occupancyTypeId &&
            row.mealPlanId === mealPlanId &&
            row.seasonalPeriodId === seasonalPeriodId
          ) {
            // Update the field value first
            const updatedWeekdayValues = {
              ...row.weekdayValues,
              [day]: {
                ...row.weekdayValues[day],
                [field]: value,
              },
            };

            // MUTUAL EXCLUSION LOGIC: Clear the opposite margin field when one is entered
            if (field === "fixedMargin" && value > 0) {
              // When Fixed Margin is entered, clear Margin Percentage for this weekday
              updatedWeekdayValues[day] = {
                ...updatedWeekdayValues[day],
                marginPercentage: 0,
              };
              console.log(
                `🔄 Mutual exclusion: Cleared ${day} margin percentage because fixed margin was set to ${value}`
              );
            } else if (field === "marginPercentage" && value > 0) {
              // When Margin Percentage is entered, clear Fixed Margin for this weekday
              updatedWeekdayValues[day] = {
                ...updatedWeekdayValues[day],
                fixedMargin: 0,
              };
              console.log(
                `🔄 Mutual exclusion: Cleared ${day} fixed margin because margin percentage was set to ${value}`
              );
            }

            // Automatically calculate total when cost/margin values change
            const dayValues = updatedWeekdayValues[day];

            // Use updated values for calculation, fall back to default if not set
            const grossCost =
              dayValues.grossCost !== undefined && dayValues.grossCost !== null
                ? dayValues.grossCost
                : row.defaultValues.grossCost;
            const fixedMargin =
              dayValues.fixedMargin !== undefined &&
              dayValues.fixedMargin !== null
                ? dayValues.fixedMargin
                : row.defaultValues.fixedMargin;
            // Use weekday-specific margin percentage if set, otherwise fall back to default
            const marginPercentage =
              dayValues.marginPercentage !== undefined &&
              dayValues.marginPercentage !== null
                ? dayValues.marginPercentage
                : row.defaultValues.marginPercentage;

            // Always calculate total, even when cost is 0
            const calculatedTotalValue = calculateTotalFromCostMargin({
              gross_cost: grossCost, // Backend handles conversion (0 is valid)
              fixed_margin: fixedMargin, // Backend handles conversion
              margin_percentage: marginPercentage, // Percentage stays as-is
            });

            let calculatedTotal = dayValues.total;
            if (
              calculatedTotalValue !== null &&
              calculatedTotalValue !== undefined
            ) {
              calculatedTotal = calculatedTotalValue; // Use calculated value directly
              console.log(
                `🔄 Auto-calculated ${day} total: ${calculatedTotal} CHF (cost=${grossCost}, margin=${fixedMargin}, %=${marginPercentage})`
              );
            }

            // Update the total in weekday values
            updatedWeekdayValues[day] = {
              ...updatedWeekdayValues[day],
              total: calculatedTotal,
            };

            return {
              ...row,
              weekdayValues: updatedWeekdayValues,
              // Also update the prices field for backward compatibility
              prices: {
                ...row.prices,
                [day]: calculatedTotal,
              },
              modified: true,
            };
          }
          return row;
        })
      );
    },
    []
  );

  // Handle applying weekday-specific values to all rows (LOCAL CALCULATION ONLY)
  const handleApplyWeekdayToAllRows = (
    day: "mon" | "tue" | "wed" | "thu" | "fri" | "sat" | "sun",
    sourceRow: PricingRow
  ) => {
    const sourceValues = sourceRow.weekdayValues[day];

    setPricingRows((prev) =>
      prev.map((row) => {
        // Apply to all rows in the same seasonal period (or base pricing)
        if (row.seasonalPeriodId === sourceRow.seasonalPeriodId) {
          const updatedWeekdayValues = {
            ...row.weekdayValues,
            [day]: {
              grossCost: sourceValues.grossCost,
              fixedMargin: sourceValues.fixedMargin,
              marginPercentage: sourceValues.marginPercentage,
              total: sourceValues.total,
            },
          };

          // Update the corresponding price in the prices object
          const updatedPrices = {
            ...row.prices,
            [day]: sourceValues.total,
          };

          return {
            ...row,
            weekdayValues: updatedWeekdayValues,
            prices: updatedPrices,
            modified: true,
          };
        }
        return row;
      })
    );
  };

  // Function to actually perform the apply to all days operation
  const performApplyToAllDays = useCallback(
    (
      roomConfigId: string,
      occupancyTypeId: string,
      mealPlanId: string | null,
      seasonalPeriodId: string | undefined,
      pricingRow: PricingRow
    ) => {
      console.log(
        `🔄 Applying default cost/margin values (cost=${pricingRow.defaultValues.grossCost}, margin=${pricingRow.defaultValues.fixedMargin}, %=${pricingRow.defaultValues.marginPercentage}) to all weekday fields (LOCAL ONLY)`
      );

      // Use the calculated default total for all weekdays
      const defaultTotal = pricingRow.defaultValues.total;
      const weekdays: (keyof typeof pricingRow.weekdayValues)[] = [
        "mon",
        "tue",
        "wed",
        "thu",
        "fri",
        "sat",
        "sun",
      ];
      const calculatedTotals: { [key: string]: number } = {};
      const calculatedPrices: { [key: string]: number } = {};

      weekdays.forEach((day) => {
        // Apply the default total to all weekdays
        calculatedTotals[day] = defaultTotal;
        calculatedPrices[day] = defaultTotal;

        console.log(
          `  ${day}: Applied cost=${pricingRow.defaultValues.grossCost}, margin=${pricingRow.defaultValues.fixedMargin}, %=${pricingRow.defaultValues.marginPercentage} → total=${defaultTotal} CHF`
        );
      });

      // Update the local state to populate all weekday fields with the default cost/margin values
      setPricingRows((prev) =>
        prev.map((row) => {
          if (
            row.roomConfigId === roomConfigId &&
            row.occupancyTypeId === occupancyTypeId &&
            row.mealPlanId === mealPlanId &&
            row.seasonalPeriodId === seasonalPeriodId
          ) {
            return {
              ...row,
              // Update weekday values with default cost/margin values copied to all days
              weekdayValues: {
                mon: {
                  grossCost: pricingRow.defaultValues.grossCost,
                  fixedMargin: pricingRow.defaultValues.fixedMargin,
                  marginPercentage: pricingRow.defaultValues.marginPercentage,
                  total: calculatedTotals.mon,
                },
                tue: {
                  grossCost: pricingRow.defaultValues.grossCost,
                  fixedMargin: pricingRow.defaultValues.fixedMargin,
                  marginPercentage: pricingRow.defaultValues.marginPercentage,
                  total: calculatedTotals.tue,
                },
                wed: {
                  grossCost: pricingRow.defaultValues.grossCost,
                  fixedMargin: pricingRow.defaultValues.fixedMargin,
                  marginPercentage: pricingRow.defaultValues.marginPercentage,
                  total: calculatedTotals.wed,
                },
                thu: {
                  grossCost: pricingRow.defaultValues.grossCost,
                  fixedMargin: pricingRow.defaultValues.fixedMargin,
                  marginPercentage: pricingRow.defaultValues.marginPercentage,
                  total: calculatedTotals.thu,
                },
                fri: {
                  grossCost: pricingRow.defaultValues.grossCost,
                  fixedMargin: pricingRow.defaultValues.fixedMargin,
                  marginPercentage: pricingRow.defaultValues.marginPercentage,
                  total: calculatedTotals.fri,
                },
                sat: {
                  grossCost: pricingRow.defaultValues.grossCost,
                  fixedMargin: pricingRow.defaultValues.fixedMargin,
                  marginPercentage: pricingRow.defaultValues.marginPercentage,
                  total: calculatedTotals.sat,
                },
                sun: {
                  grossCost: pricingRow.defaultValues.grossCost,
                  fixedMargin: pricingRow.defaultValues.fixedMargin,
                  marginPercentage: pricingRow.defaultValues.marginPercentage,
                  total: calculatedTotals.sun,
                },
              },
              // Also update prices for backward compatibility
              prices: {
                mon: calculatedPrices.mon,
                tue: calculatedPrices.tue,
                wed: calculatedPrices.wed,
                thu: calculatedPrices.thu,
                fri: calculatedPrices.fri,
                sat: calculatedPrices.sat,
                sun: calculatedPrices.sun,
              },
              modified: true,
              appliedToAllDays: true, // Mark that Apply button has been used
            };
          }
          return row;
        })
      );

      // Show success message
      toast.success("Applied default cost/margin values to all weekdays", {
        description: `Copied default cost (${pricingRow.defaultValues.grossCost}), margin (${pricingRow.defaultValues.fixedMargin}), and percentage (${pricingRow.defaultValues.marginPercentage}%) to all 7 days`,
      });
    },
    []
  );

  // Handle applying cost/margin calculations to all weekdays with confirmation prompt
  const handleApplyToAllDays = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined
  ) => {
    // Find the pricing row
    const pricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
    );

    if (!pricingRow) {
      toast.error("Error", {
        description: "Pricing row not found",
      });
      return;
    }

    // Check if we have valid default total (should be auto-calculated now)
    if (
      !pricingRow.defaultValues.total ||
      pricingRow.defaultValues.total <= 0
    ) {
      toast.error("Error", {
        description:
          "Please set valid default cost/margin values first. The total should be automatically calculated.",
      });
      return;
    }

    // Check if any weekday already has values (non-zero cost, margin, or percentage)
    const weekdays: (keyof typeof pricingRow.weekdayValues)[] = [
      "mon",
      "tue",
      "wed",
      "thu",
      "fri",
      "sat",
      "sun",
    ];

    const existingValues = weekdays.some((day) => {
      const dayValues = pricingRow.weekdayValues[day];
      return (
        (dayValues.grossCost && dayValues.grossCost > 0) ||
        (dayValues.fixedMargin && dayValues.fixedMargin > 0) ||
        (dayValues.marginPercentage && dayValues.marginPercentage > 0)
      );
    });

    // If there are existing values, show confirmation prompt
    if (existingValues) {
      setPendingApplyToAllDaysData({
        roomConfigId,
        occupancyTypeId,
        mealPlanId,
        seasonalPeriodId,
        pricingRow,
      });
      setIsApplyToAllDaysPromptOpen(true);
    } else {
      // No existing values, apply directly
      performApplyToAllDays(
        roomConfigId,
        occupancyTypeId,
        mealPlanId,
        seasonalPeriodId,
        pricingRow
      );
    }
  };

  // Handle confirmation of apply to all days
  const handleConfirmApplyToAllDays = () => {
    if (pendingApplyToAllDaysData) {
      const {
        roomConfigId,
        occupancyTypeId,
        mealPlanId,
        seasonalPeriodId,
        pricingRow,
      } = pendingApplyToAllDaysData;
      performApplyToAllDays(
        roomConfigId,
        occupancyTypeId,
        mealPlanId,
        seasonalPeriodId,
        pricingRow
      );
    }
    setIsApplyToAllDaysPromptOpen(false);
    setPendingApplyToAllDaysData(null);
  };

  // Handle cancellation of apply to all days
  const handleCancelApplyToAllDays = () => {
    setIsApplyToAllDaysPromptOpen(false);
    setPendingApplyToAllDaysData(null);
  };

  const handleSaveAll = async () => {
    setIsSaving(true);

    try {
      console.log("=== SAVE ALL DEBUG START ===");

      // Group pricing rows by room config and seasonal period
      const roomConfigPrices: Record<string, any> = {};

      // Only initialize room config prices for rooms that have modified data
      // Don't create empty pricing records for all rooms

      // Process base pricing rows
      const basePricingRows = pricingRows.filter(
        (row) => !row.seasonalPeriodId
      );
      basePricingRows.forEach((row) => {
        // Only include rows that have been modified
        if (row.modified) {
          console.log(
            `Saving modified base pricing for room ${row.roomConfigId}, currency ${currencyCode}`
          );

          // Initialize room config pricing if not exists
          if (!roomConfigPrices[row.roomConfigId]) {
            roomConfigPrices[row.roomConfigId] = {
              currency_code: currencyCode,
              weekday_rules: [],
              seasonal_prices: [],
            };
          }

          // Create default values (backend handles conversion, send display values directly)
          const defaultValues = {
            gross_cost: row.defaultValues.grossCost || 0, // Send display value directly
            fixed_margin: row.defaultValues.fixedMargin || 0, // Send display value directly
            margin_percentage: row.defaultValues.marginPercentage || 0, // Percentage stays as-is
            total: row.defaultValues.total || 0, // Send display value directly
          };

          // Create weekday prices (backend handles conversion, send display values directly)
          const weekdayPrices = {
            mon: row.prices.mon || 0, // Send display value directly
            tue: row.prices.tue || 0, // Send display value directly
            wed: row.prices.wed || 0, // Send display value directly
            thu: row.prices.thu || 0, // Send display value directly
            fri: row.prices.fri || 0, // Send display value directly
            sat: row.prices.sat || 0, // Send display value directly
            sun: row.prices.sun || 0, // Send display value directly
          };

          // Create weekday-specific cost and margin values (backend handles conversion, send display values directly)
          const weekdayValues = {
            mon: {
              gross_cost: row.weekdayValues.mon.grossCost || 0, // Send display value directly
              fixed_margin: row.weekdayValues.mon.fixedMargin || 0, // Send display value directly
              margin_percentage: row.weekdayValues.mon.marginPercentage || 0, // Percentage stays as-is
            },
            tue: {
              gross_cost: row.weekdayValues.tue.grossCost || 0, // Send display value directly
              fixed_margin: row.weekdayValues.tue.fixedMargin || 0, // Send display value directly
              margin_percentage: row.weekdayValues.tue.marginPercentage || 0, // Percentage stays as-is
            },
            wed: {
              gross_cost: row.weekdayValues.wed.grossCost || 0, // Send display value directly
              fixed_margin: row.weekdayValues.wed.fixedMargin || 0, // Send display value directly
              margin_percentage: row.weekdayValues.wed.marginPercentage || 0, // Percentage stays as-is
            },
            thu: {
              gross_cost: row.weekdayValues.thu.grossCost || 0, // Send display value directly
              fixed_margin: row.weekdayValues.thu.fixedMargin || 0, // Send display value directly
              margin_percentage: row.weekdayValues.thu.marginPercentage || 0, // Percentage stays as-is
            },
            fri: {
              gross_cost: row.weekdayValues.fri.grossCost || 0, // Send display value directly
              fixed_margin: row.weekdayValues.fri.fixedMargin || 0, // Send display value directly
              margin_percentage: row.weekdayValues.fri.marginPercentage || 0, // Percentage stays as-is
            },
            sat: {
              gross_cost: row.weekdayValues.sat.grossCost || 0, // Send display value directly
              fixed_margin: row.weekdayValues.sat.fixedMargin || 0, // Send display value directly
              margin_percentage: row.weekdayValues.sat.marginPercentage || 0, // Percentage stays as-is
            },
            sun: {
              gross_cost: row.weekdayValues.sun.grossCost || 0, // Send display value directly
              fixed_margin: row.weekdayValues.sun.fixedMargin || 0, // Send display value directly
              margin_percentage: row.weekdayValues.sun.marginPercentage || 0, // Percentage stays as-is
            },
          };

          console.log(
            `[SAVE ALL] 💰 Base pricing data for room ${row.roomConfigId}:`,
            {
              row_info: {
                roomConfigId: row.roomConfigId,
                occupancyTypeId: row.occupancyTypeId,
                mealPlanId: row.mealPlanId,
                modified: row.modified,
              },
              display_values: {
                defaultValues: row.defaultValues,
                weekdayPrices: row.prices,
              },
              api_payload: {
                defaultValues: defaultValues,
                weekdayPrices: weekdayPrices,
              },
              note: "Backend handles currency conversion - sending display values directly",
            }
          );

          roomConfigPrices[row.roomConfigId].weekday_rules.push({
            occupancy_type_id: row.occupancyTypeId,
            meal_plan_id: row.mealPlanId, // This will be null for extra beds
            default_values: defaultValues,
            weekday_prices: weekdayPrices,
            weekday_values: weekdayValues, // Include weekday-specific cost and margin data
          });
        }
      });

      // Process seasonal pricing rows - Use enhanced bulk API with cost/margin support
      const seasonalPricingRows = pricingRows.filter(
        (row) => row.seasonalPeriodId && row.modified
      );

      console.log(
        `=== SEASONAL PRICING: ${seasonalPricingRows.length} seasonal pricing rules to update ===`
      );

      // Group seasonal pricing rows by room config and seasonal period for bulk API calls
      const seasonalPricingGroups: Record<string, any> = {};

      seasonalPricingRows.forEach((row) => {
        const seasonalPeriod = seasonalPeriods.find(
          (s) => s.id === row.seasonalPeriodId
        );
        if (!seasonalPeriod) {
          console.error(
            `❌ Seasonal period not found for ID: ${row.seasonalPeriodId}`
          );
          return;
        }

        const groupKey = `${row.roomConfigId}_${row.seasonalPeriodId}`;

        console.log(
          `Grouping seasonal pricing for room ${row.roomConfigId}, season ${seasonalPeriod.name} (ID: ${seasonalPeriod.id})`
        );
        console.log({ row, seasonalPeriod });

        if (!seasonalPricingGroups[groupKey]) {
          seasonalPricingGroups[groupKey] = {
            roomConfigId: row.roomConfigId,
            seasonalPeriod: seasonalPeriod,
            weekday_rules: [],
          };
        }

        // Create enhanced weekday rule with cost/margin data (backend handles conversion, send display values directly)
        const defaultValues = {
          gross_cost: row.defaultValues.grossCost || 0, // Send display value directly
          fixed_margin: row.defaultValues.fixedMargin || 0, // Send display value directly
          margin_percentage: row.defaultValues.marginPercentage || 0,
          total: row.defaultValues.total || 0, // Send display value directly
        };

        const weekdayValues = {
          mon: {
            gross_cost: row.weekdayValues.mon.grossCost || 0, // Send display value directly
            fixed_margin: row.weekdayValues.mon.fixedMargin || 0, // Send display value directly
            margin_percentage: row.weekdayValues.mon.marginPercentage || 0,
          },
          tue: {
            gross_cost: row.weekdayValues.tue.grossCost || 0, // Send display value directly
            fixed_margin: row.weekdayValues.tue.fixedMargin || 0, // Send display value directly
            margin_percentage: row.weekdayValues.tue.marginPercentage || 0,
          },
          wed: {
            gross_cost: row.weekdayValues.wed.grossCost || 0, // Send display value directly
            fixed_margin: row.weekdayValues.wed.fixedMargin || 0, // Send display value directly
            margin_percentage: row.weekdayValues.wed.marginPercentage || 0,
          },
          thu: {
            gross_cost: row.weekdayValues.thu.grossCost || 0, // Send display value directly
            fixed_margin: row.weekdayValues.thu.fixedMargin || 0, // Send display value directly
            margin_percentage: row.weekdayValues.thu.marginPercentage || 0,
          },
          fri: {
            gross_cost: row.weekdayValues.fri.grossCost || 0, // Send display value directly
            fixed_margin: row.weekdayValues.fri.fixedMargin || 0, // Send display value directly
            margin_percentage: row.weekdayValues.fri.marginPercentage || 0,
          },
          sat: {
            gross_cost: row.weekdayValues.sat.grossCost || 0, // Send display value directly
            fixed_margin: row.weekdayValues.sat.fixedMargin || 0, // Send display value directly
            margin_percentage: row.weekdayValues.sat.marginPercentage || 0,
          },
          sun: {
            gross_cost: row.weekdayValues.sun.grossCost || 0, // Send display value directly
            fixed_margin: row.weekdayValues.sun.fixedMargin || 0, // Send display value directly
            margin_percentage: row.weekdayValues.sun.marginPercentage || 0,
          },
        };

        const weekdayPrices = {
          mon: row.prices.mon || 0, // Send display value directly
          tue: row.prices.tue || 0, // Send display value directly
          wed: row.prices.wed || 0, // Send display value directly
          thu: row.prices.thu || 0, // Send display value directly
          fri: row.prices.fri || 0, // Send display value directly
          sat: row.prices.sat || 0, // Send display value directly
          sun: row.prices.sun || 0, // Send display value directly
        };

        seasonalPricingGroups[groupKey].weekday_rules.push({
          occupancy_type_id: row.occupancyTypeId,
          meal_plan_id: row.mealPlanId,
          default_values: defaultValues,
          weekday_values: weekdayValues,
          weekday_prices: weekdayPrices,
        });
      });

      // Call enhanced bulk seasonal pricing API for each group
      const seasonalPricingPromises = Object.values(seasonalPricingGroups).map(
        async (group: any) => {
          console.log(
            `🌟 SEASONAL BULK API CALL: Updating seasonal pricing for room ${group.roomConfigId}, season ${group.seasonalPeriod.name} with ${group.weekday_rules.length} rules`
          );

          const payload = {
            currency_code: currencyCode,
            name: group.seasonalPeriod.name,
            start_date: group.seasonalPeriod.start_date,
            end_date: group.seasonalPeriod.end_date,
            // Include seasonal_period_id to ensure we update existing season instead of creating new one
            seasonal_period_id: group.seasonalPeriod.id,
            weekday_rules: group.weekday_rules,
          };

          console.log(
            `[FRONTEND] 🌟 Seasonal pricing data for room ${group.roomConfigId}:`,
            {
              seasonal_period_id: group.seasonalPeriod.id,
              seasonal_period_name: group.seasonalPeriod.name,
              display_values: group.weekday_rules.map((rule: any) => ({
                occupancy_type_id: rule.occupancy_type_id,
                default_values: rule.default_values,
                weekday_prices: {
                  mon: rule.weekday_prices.mon,
                  tue: rule.weekday_prices.tue,
                },
              })),
              api_payload: payload,
              note: "Backend handles currency conversion - sending display values directly",
            }
          );

          console.log(`[FRONTEND] 📤 Sending seasonal pricing payload:`, {
            seasonal_period_id: payload.seasonal_period_id,
            name: payload.name,
            full_payload: payload,
          });

          const response = await fetch(
            `/admin/hotel-management/room-configs/${group.roomConfigId}/seasonal-pricing/bulk`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify(payload),
            }
          );

          if (!response.ok) {
            const errorText = await response.text();
            console.error(
              `Failed to update seasonal pricing for room ${group.roomConfigId}: ${errorText}`
            );
            throw new Error(`Failed to update seasonal pricing: ${errorText}`);
          }

          const result = await response.json();
          console.log(
            `✅ Successfully updated seasonal pricing for room ${group.roomConfigId}, season ${group.seasonalPeriod.name}`
          );
          return result;
        }
      );

      // Wait for all seasonal pricing groups to be updated
      const seasonalResults = await Promise.all(seasonalPricingPromises);

      // Save base pricing for each room config
      // FIXED: Only save room configs that actually have base pricing changes
      // Now that seasonal pricing is handled separately, roomConfigPrices should
      // only contain room configs with actual base pricing modifications
      console.log(
        `=== BASE PRICING: ${
          Object.keys(roomConfigPrices).length
        } room configs to process ===`
      );
      const savePromises = Object.entries(roomConfigPrices).map(
        async ([roomConfigId, data]) => {
          // Only save if there are weekday rules (base pricing)
          if (data.weekday_rules.length > 0) {
            console.log(
              `💰 BASE API CALL: Saving base pricing for room config ${roomConfigId} with ${data.weekday_rules.length} rules`
            );

            // FIXED: Use direct fetch instead of savePricing hook to avoid duplicate seasonal processing
            // The savePricing hook automatically processes seasonal_prices which we handle separately
            const response = await fetch(
              `/admin/hotel-management/room-configs/${roomConfigId}/weekday-pricing/bulk`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  currency_code: data.currency_code,
                  weekday_rules: data.weekday_rules,
                }),
              }
            );

            if (!response.ok) {
              const errorText = await response.text();
              console.error(
                `Failed to save base pricing for room ${roomConfigId}: ${errorText}`
              );
              throw new Error(`Failed to save base pricing: ${errorText}`);
            }

            return await response.json();
          } else {
            console.log(
              `⏭️  SKIPPING: Room config ${roomConfigId} has no base pricing changes`
            );
          }
          return null;
        }
      );

      const baseResults = await Promise.all(savePromises);
      const results = [...baseResults, ...seasonalResults];

      console.log(
        `=== SAVE ALL DEBUG END: ${seasonalResults.length} seasonal + ${
          baseResults.filter((r) => r !== null).length
        } base = ${
          results.filter((r) => r !== null).length
        } total API calls ===`
      );

      // Call onSave callback if provided and wait for it to complete
      if (onSave) {
        await onSave({
          results,
          seasonal_periods: seasonalPeriods,
        });
      }

      // Reset modified flags AFTER the data has been refreshed
      setPricingRows((prev) =>
        prev.map((row) => ({ ...row, modified: false }))
      );

      // Don't show success toast here - let the parent component handle it
      // The parent component will show the success message after refetching data
    } catch (error) {
      console.error("Error saving pricing:", error);
      toast.error("Error", {
        description: "Failed to save pricing",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Filter pricing rows based on selected filters - memoized for performance
  const filteredPricingRows = useMemo(() => {
    return pricingRows.filter((row) => {
      // Filter by room config if selected
      if (selectedRoomConfig && row.roomConfigId !== selectedRoomConfig) {
        return false;
      }

      // Filter by occupancy config if selected
      if (
        selectedOccupancyConfig &&
        row.occupancyTypeId !== selectedOccupancyConfig
      ) {
        return false;
      }

      // Filter by meal plan if selected
      if (selectedMealPlan && row.mealPlanId !== selectedMealPlan) {
        return false;
      }

      // Filter by season based on selected season filter
      if (selectedSeasonFilter === "base" && row.seasonalPeriodId) {
        return false; // Hide seasonal rows when showing base only
      }
      if (selectedSeasonFilter !== "all" && selectedSeasonFilter !== "base") {
        // Show only specific season
        if (row.seasonalPeriodId !== selectedSeasonFilter) {
          return false;
        }
      }

      return true;
    });
  }, [
    pricingRows,
    selectedRoomConfig,
    selectedOccupancyConfig,
    selectedMealPlan,
    selectedSeasonFilter,
  ]);

  if (isLoading) {
    return <HotelPricingTableSkeleton />;
  }

  // Reusable select component using @camped-ai/ui Select
  const FilterSelect = ({
    id,
    label,
    value,
    onChange,
    options,
    placeholder = "All",
  }: {
    id: string;
    label: string;
    value: string;
    onChange: (value: string) => void;
    options: Array<{ id: string; name?: string; title?: string }>;
    placeholder?: string;
  }) => (
    <div>
      <Label htmlFor={id} className="mb-1 block text-sm font-medium">
        {label}
      </Label>
      <Select
        value={value || "all"}
        onValueChange={(val) => onChange(val === "all" ? "" : val)}
      >
        <Select.Trigger id={id} className="w-full">
          <Select.Value placeholder={placeholder} />
        </Select.Trigger>
        <Select.Content>
          <Select.Item value="all">{placeholder}</Select.Item>
          {options.map((option) => (
            <Select.Item key={option.id} value={option.id}>
              {option.title || option.name}
            </Select.Item>
          ))}
        </Select.Content>
      </Select>
    </div>
  );

  return (
    <Container>
      <Toaster />

      {/* Compact Header with Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
        {!hideBackButton && !readOnlyMode && (
          <div>
            <Heading level="h2" className="mb-1">
              Hotel Pricing Management
            </Heading>
            <Text className=" text-sm">
              Manage base rates, seasonal pricing, and special offers for your
              hotel rooms
            </Text>
          </div>
        )}

        {readOnlyMode && (
          <div>
            <Heading level="h2" className="mb-1">
              Hotel Pricing Overview
            </Heading>
            <Text className=" text-sm">
              View current pricing for all room types and seasonal periods
            </Text>
          </div>
        )}

        <div className="flex flex-wrap items-center gap-2">
          {/* {currencies.length > 1 && (
            <CurrencySelector
              value={currencyCode}
              onChange={readOnlyMode ? () => {} : handleCurrencyChange}
              label="Currency"
              id="currency"
              disabled={readOnlyMode}
            />
          )} */}

          {/* Bulk Import button - available only in edit mode */}
          {canEdit && !readOnlyMode && (
            <Button
              variant="secondary"
              onClick={() => setIsBulkImportModalOpen(true)}
              className="flex items-center gap-2 h-9 text-sm"
              disabled={isLoading}
            >
              <Upload className="w-4 h-4" />
              Bulk Import
            </Button>
          )}

          {/* Export button - available in both edit and read-only modes */}
          <Button
            variant="secondary"
            onClick={() => setIsExportModalOpen(true)}
            className="flex items-center gap-2 h-9 text-sm"
            disabled={isLoading}
          >
            <Download className="w-4 h-4" />
            Export
          </Button>

          {canEdit && !readOnlyMode && (
            <Button
              variant="primary"
              onClick={handleSaveAll}
              className="flex items-center gap-2 h-9 text-sm"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Save All
                </>
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Bulk Price Update Modal - Hidden in read-only mode */}
      {!readOnlyMode && (
        <BulkPriceUpdateModal
          isOpen={isBulkUpdateModalOpen}
          onClose={() => setIsBulkUpdateModalOpen(false)}
          roomConfigs={roomConfigs}
          occupancyConfigs={occupancyConfigs}
          mealPlans={mealPlans}
          seasonalPeriods={seasonalPeriods}
          currencyCode={currencyCode}
          pricingRows={pricingRows}
          onApplyUpdate={handleBulkPriceUpdate}
          onCurrencyChange={handleCurrencyChange}
        />
      )}

      {/* Compact Filters */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-6 p-4 bg-muted/50 rounded-lg border border-border">
        {/* Seasons Filter using @camped-ai/ui Select */}
        <div>
          <Label
            htmlFor="showAllSeasons"
            className="mb-1 block text-sm font-medium"
          >
            Season
          </Label>
          <Select
            value={selectedSeasonFilter}
            onValueChange={setSelectedSeasonFilter}
          >
            <Select.Trigger id="showAllSeasons" className="w-full">
              <Select.Value placeholder="All Seasons" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="all">All Seasons</Select.Item>
              <Select.Item value="base">Base Pricing Only</Select.Item>
              {seasonalPeriods.map((season) => (
                <Select.Item key={season.id} value={season.id}>
                  {season.name}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        <FilterSelect
          id="roomConfig"
          label="Room Type"
          value={selectedRoomConfig}
          onChange={setSelectedRoomConfig}
          options={roomConfigs}
          placeholder="All Room Types"
        />

        <FilterSelect
          id="occupancyConfig"
          label="Occupancy Type"
          value={selectedOccupancyConfig}
          onChange={setSelectedOccupancyConfig}
          options={occupancyConfigs}
          placeholder="All Occupancy Types"
        />

        <FilterSelect
          id="mealPlan"
          label="Meal Plan"
          value={selectedMealPlan}
          onChange={setSelectedMealPlan}
          options={mealPlans}
          placeholder="All Meal Plans"
        />
      </div>

      <div className="overflow-x-auto rounded-lg border border-border shadow-sm">
        <table className="min-w-full divide-y divide-border table-fixed">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                Season
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                Room Type
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                Occupancy
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-28">
                Meal Plan
              </th>

              {/* Cost & Margin Management Columns - Hidden in read-only mode */}
              {!readOnlyMode && (
                <>
                  <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                    <div className="flex flex-col items-center gap-0.5">
                      <span className="font-semibold">Gross Cost</span>
                      <span className="text-xs text-muted-foreground/70 font-normal">
                        {getCurrencySymbol()}
                      </span>
                    </div>
                  </th>
                  <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                    <div className="flex flex-col items-center gap-0.5">
                      <span className="font-semibold">Fixed Margin</span>
                      <span className="text-xs text-muted-foreground/70 font-normal">
                        {getCurrencySymbol()}
                      </span>
                    </div>
                  </th>
                  <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                    <div className="flex flex-col items-center gap-0.5">
                      <span className="font-semibold">Margin %</span>
                    </div>
                  </th>
                  <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-24">
                    <div className="flex flex-col items-center gap-0.5">
                      <span className="font-semibold">Total</span>
                      <span className="text-xs text-muted-foreground/70 font-normal">
                        {getCurrencySymbol()}
                      </span>
                    </div>
                  </th>
                  <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                    Actions
                  </th>
                </>
              )}

              {/* Grouped Weekday Columns */}
              {showAllDays ? (
                <>
                  {weekdays.map((day, index) => (
                    <th
                      key={day.id}
                      className={`px-1 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide ${
                        index === 0
                          ? "border-l-2 border-l-blue-400"
                          : "border-l-2 border-l-blue-300"
                      } ${
                        index === weekdays.length - 1
                          ? "border-r-2 border-r-blue-400"
                          : ""
                      } bg-blue-50/50 dark:bg-blue-950/20`}
                      colSpan={readOnlyMode ? 1 : 4}
                    >
                      <div className="flex flex-col items-center gap-0.5">
                        <span className="font-semibold text-blue-600 dark:text-blue-400">
                          {day.name}
                        </span>
                        {!readOnlyMode && (
                          <div className="flex items-center justify-between gap-1 text-xs w-full px-1">
                            <span className="text-center flex-1">Cost</span>
                            <span className="text-center flex-1">Margin</span>
                            <span className="text-center flex-1">%</span>
                            <span className="text-center flex-1">Price</span>
                          </div>
                        )}
                        {readOnlyMode && (
                          <span className="text-xs text-muted-foreground/70 font-normal">
                            {getCurrencySymbol()}
                          </span>
                        )}
                      </div>
                    </th>
                  ))}
                </>
              ) : (
                <th className="px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide">
                  <div className="flex flex-col items-center gap-0.5">
                    <span className="font-semibold">Price</span>
                    <span className="text-xs text-muted-foreground/70 font-normal">
                      {getCurrencySymbol()}
                    </span>
                  </div>
                </th>
              )}
              {/* <th className="px-2 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wide">
                Actions
              </th> */}
            </tr>
          </thead>
          <tbody className="bg-background divide-y divide-border">
            {filteredPricingRows.map((row, index) => {
              // Find the room, occupancy, and meal plan objects
              const room = roomConfigs.find((r) => r.id === row.roomConfigId);
              const occupancy = occupancyConfigs.find(
                (o) => o.id === row.occupancyTypeId
              );
              const mealPlan = mealPlans.find((m) => m.id === row.mealPlanId);
              const seasonalPeriod = row.seasonalPeriodId
                ? seasonalPeriods.find((s) => s.id === row.seasonalPeriodId)
                : undefined;

              return (
                <tr
                  key={row.id}
                  className={index % 2 === 0 ? "bg-background" : "bg-muted/50"}
                >
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    {seasonalPeriod ? (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                        <div className="flex flex-col min-w-0">
                          <span
                            className="font-medium truncate text-xs"
                            title={seasonalPeriod.name}
                          >
                            {seasonalPeriod.name}
                          </span>
                          <span className="text-xs text-muted-foreground truncate">
                            {format(
                              new Date(seasonalPeriod.start_date),
                              "MMM d"
                            )}{" "}
                            -{" "}
                            {format(new Date(seasonalPeriod.end_date), "MMM d")}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <span className="font-medium text-foreground text-xs">
                        Base Price
                      </span>
                    )}
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    <div
                      className="truncate text-xs"
                      title={room?.title || "Unknown Room"}
                    >
                      <span className="font-medium">
                        {room?.title || "Unknown Room"}
                      </span>
                    </div>
                  </td>
                  <td className="px-2 py-3 whitespace-nowrap text-sm w-20">
                    <div
                      className="truncate text-xs"
                      title={occupancy?.name || "Unknown Occupancy"}
                    >
                      {occupancy?.name || "Unknown Occupancy"}
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-28">
                    <div
                      className="truncate"
                      title={
                        (occupancy as any)?.type === "EXTRA_BED" ||
                        occupancy?.name?.toLowerCase().includes("extra bed") ||
                        (occupancy as any)?.type === "COT" ||
                        occupancy?.name?.toLowerCase().includes("cot")
                          ? "-"
                          : mealPlan?.name || "Unknown Meal Plan"
                      }
                    >
                      {(occupancy as any)?.type === "EXTRA_BED" ||
                      occupancy?.name?.toLowerCase().includes("extra bed") ||
                      (occupancy as any)?.type === "COT" ||
                      occupancy?.name?.toLowerCase().includes("cot") ? (
                        <span className="text-muted-foreground">-</span>
                      ) : (
                        mealPlan?.name || "Unknown Meal Plan"
                      )}
                    </div>
                  </td>

                  {/* Cost & Margin Management Columns - Hidden in read-only mode */}
                  {!readOnlyMode && (
                    <>
                      {/* Gross Cost */}
                      <td className="px-2 py-3 whitespace-nowrap">
                        <div className="flex justify-center">
                          <DebouncedInput
                            type="text"
                            className={`w-20 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                              row.modified
                                ? "border-primary bg-primary/10 dark:bg-primary/20"
                                : "border-input"
                            }`}
                            value={row.defaultValues.grossCost.toString()}
                            onChange={(value) => {
                              const validatedValue =
                                validateNumericInput(value);
                              handleDefaultValueChange(
                                row.roomConfigId,
                                row.occupancyTypeId,
                                row.mealPlanId,
                                row.seasonalPeriodId,
                                "grossCost",
                                parseFloat(validatedValue) || 0
                              );
                            }}
                            onKeyDown={(e) => handleNumericKeyPress(e)}
                            placeholder="0.00"
                            debounceMs={300}
                          />
                        </div>
                      </td>

                      {/* Fixed Margin */}
                      <td className="px-2 py-3 whitespace-nowrap">
                        <div className="flex justify-center">
                          <DebouncedInput
                            type="text"
                            className={`w-20 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                              row.modified
                                ? "border-primary bg-primary/10 dark:bg-primary/20"
                                : "border-input"
                            }`}
                            value={row.defaultValues.fixedMargin.toString()}
                            onChange={(value) => {
                              const validatedValue =
                                validateNumericInput(value);
                              handleDefaultValueChange(
                                row.roomConfigId,
                                row.occupancyTypeId,
                                row.mealPlanId,
                                row.seasonalPeriodId,
                                "fixedMargin",
                                parseFloat(validatedValue) || 0
                              );
                            }}
                            onKeyDown={(e) => handleNumericKeyPress(e)}
                            placeholder="0.00"
                            debounceMs={300}
                          />
                        </div>
                      </td>

                      {/* Margin Percentage */}
                      <td className="px-2 py-3 whitespace-nowrap">
                        <div className="flex justify-center">
                          <DebouncedInput
                            type="text"
                            className={`w-16 py-1 text-sm border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                              row.modified
                                ? "border-primary bg-primary/10 dark:bg-primary/20"
                                : "border-input"
                            }`}
                            value={row.defaultValues.marginPercentage.toString()}
                            onChange={(value) => {
                              const validatedValue = validateNumericInput(
                                value,
                                true
                              );
                              handleDefaultValueChange(
                                row.roomConfigId,
                                row.occupancyTypeId,
                                row.mealPlanId,
                                row.seasonalPeriodId,
                                "marginPercentage",
                                parseFloat(validatedValue) || 0
                              );
                            }}
                            onKeyDown={(e) => handleNumericKeyPress(e, true)}
                            placeholder="0"
                            debounceMs={300}
                          />
                        </div>
                      </td>

                      {/* Calculated Total */}
                      <td className="px-2 py-3 whitespace-nowrap">
                        <div className="flex justify-center">
                          <div
                            className={`w-20 py-1 px-2 text-sm rounded text-center font-medium ${
                              row.defaultValues.total > 0
                                ? "bg-green-50 text-green-700 border border-green-200"
                                : "bg-gray-50 text-gray-500 border border-gray-200"
                            }`}
                          >
                            {row.defaultValues.total.toFixed(2)}
                          </div>
                        </div>
                      </td>

                      {/* Apply to All Days Action */}
                      <td className="px-2 py-3 whitespace-nowrap">
                        <div className="flex justify-center">
                          <Button
                            variant="secondary"
                            size="small"
                            onClick={() =>
                              handleApplyToAllDays(
                                row.roomConfigId,
                                row.occupancyTypeId,
                                row.mealPlanId,
                                row.seasonalPeriodId
                              )
                            }
                            disabled={
                              !canEdit ||
                              !row.defaultValues.grossCost ||
                              row.defaultValues.grossCost <= 0
                            }
                            className="flex items-center gap-1 text-xs px-2 py-1"
                            title="Apply default cost and margin values to all days of the week"
                          >
                            <Copy className="w-3 h-3" />
                            Apply
                          </Button>
                        </div>
                      </td>
                    </>
                  )}

                  {/* Grouped Weekday Cells */}
                  <>
                    {weekdays.map((day, dayIndex) => (
                      <React.Fragment key={`${row.id}-${day.id}-grouped`}>
                        {!readOnlyMode && (
                          <>
                            {/* Gross Cost */}
                            <td
                              className={`px-1 py-3 whitespace-nowrap ${
                                dayIndex === 0
                                  ? "border-l-2 border-l-blue-400"
                                  : "border-l-2 border-l-blue-300"
                              } bg-blue-50/30 dark:bg-blue-950/10`}
                            >
                              <div className="flex justify-center">
                                <Input
                                  type="text"
                                  className={`w-16 py-1 text-xs border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                    row.modified
                                      ? "border-primary bg-primary/10 dark:bg-primary/20"
                                      : "border-input"
                                  } ${
                                    !canEdit
                                      ? "bg-muted cursor-not-allowed"
                                      : ""
                                  }`}
                                  value={
                                    row.weekdayValues[
                                      day.id as keyof typeof row.weekdayValues
                                    ].grossCost
                                  }
                                  onChange={(e) => {
                                    if (!canEdit) return;
                                    const validatedValue = validateNumericInput(
                                      e.target.value
                                    );
                                    handleWeekdayValueChange(
                                      row.roomConfigId,
                                      row.occupancyTypeId,
                                      row.mealPlanId,
                                      row.seasonalPeriodId,
                                      day.id as
                                        | "mon"
                                        | "tue"
                                        | "wed"
                                        | "thu"
                                        | "fri"
                                        | "sat"
                                        | "sun",
                                      "grossCost",
                                      parseFloat(validatedValue) || 0
                                    );
                                  }}
                                  onKeyDown={(e) => handleNumericKeyPress(e)}
                                  placeholder="0.00"
                                  disabled={!canEdit}
                                />
                              </div>
                            </td>

                            {/* Fixed Margin */}
                            <td className="px-1 py-3 whitespace-nowrap bg-blue-50/30 dark:bg-blue-950/10">
                              <div className="flex justify-center">
                                <Input
                                  type="text"
                                  className={`w-16 py-1 text-xs border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                    row.modified
                                      ? "border-primary bg-primary/10 dark:bg-primary/20"
                                      : "border-input"
                                  } ${
                                    !canEdit
                                      ? "bg-muted cursor-not-allowed"
                                      : ""
                                  }`}
                                  value={
                                    row.weekdayValues[
                                      day.id as keyof typeof row.weekdayValues
                                    ].fixedMargin
                                  }
                                  onChange={(e) => {
                                    if (!canEdit) return;
                                    const validatedValue = validateNumericInput(
                                      e.target.value
                                    );
                                    handleWeekdayValueChange(
                                      row.roomConfigId,
                                      row.occupancyTypeId,
                                      row.mealPlanId,
                                      row.seasonalPeriodId,
                                      day.id as
                                        | "mon"
                                        | "tue"
                                        | "wed"
                                        | "thu"
                                        | "fri"
                                        | "sat"
                                        | "sun",
                                      "fixedMargin",
                                      parseFloat(validatedValue) || 0
                                    );
                                  }}
                                  onKeyDown={(e) => handleNumericKeyPress(e)}
                                  placeholder="0.00"
                                  disabled={!canEdit}
                                />
                              </div>
                            </td>

                            {/* Margin Percentage */}
                            <td className="px-1 py-3 whitespace-nowrap bg-blue-50/30 dark:bg-blue-950/10">
                              <div className="flex justify-center">
                                <Input
                                  type="text"
                                  className={`w-12 py-1 text-xs border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                    row.modified
                                      ? "border-primary bg-primary/10 dark:bg-primary/20"
                                      : "border-input"
                                  } ${
                                    !canEdit
                                      ? "bg-muted cursor-not-allowed"
                                      : ""
                                  }`}
                                  value={
                                    row.weekdayValues[
                                      day.id as keyof typeof row.weekdayValues
                                    ].marginPercentage
                                  }
                                  onChange={(e) => {
                                    if (!canEdit) return;
                                    const validatedValue = validateNumericInput(
                                      e.target.value,
                                      true
                                    );
                                    handleWeekdayValueChange(
                                      row.roomConfigId,
                                      row.occupancyTypeId,
                                      row.mealPlanId,
                                      row.seasonalPeriodId,
                                      day.id as
                                        | "mon"
                                        | "tue"
                                        | "wed"
                                        | "thu"
                                        | "fri"
                                        | "sat"
                                        | "sun",
                                      "marginPercentage",
                                      parseFloat(validatedValue) || 0
                                    );
                                  }}
                                  onKeyDown={(e) =>
                                    handleNumericKeyPress(e, true)
                                  }
                                  placeholder="0"
                                  disabled={!canEdit}
                                />
                              </div>
                            </td>
                          </>
                        )}

                        {/* Final Price - Always shown, but read-only in readOnlyMode */}
                        <td
                          className={`px-1 py-3 whitespace-nowrap bg-blue-50/30 dark:bg-blue-950/10 ${
                            dayIndex === weekdays.length - 1
                              ? "border-r-2 border-r-blue-400"
                              : ""
                          } ${
                            dayIndex === 0 && readOnlyMode
                              ? "border-l-2 border-l-blue-400"
                              : ""
                          }`}
                        >
                          <div className="flex justify-center">
                            {readOnlyMode ? (
                              <div className="w-16 py-1 px-2 text-xs text-center font-medium text-foreground">
                                {formatPrice(
                                  row.prices[day.id as keyof typeof row.prices]
                                )}
                              </div>
                            ) : (
                              <DebouncedInput
                                type="text"
                                className={`w-16 py-1 text-xs border rounded focus:border-primary focus:ring-1 focus:ring-primary transition-all text-center ${
                                  row.modified
                                    ? "border-primary bg-primary/10 dark:bg-primary/20"
                                    : "border-input"
                                } ${
                                  !canEdit ? "bg-muted cursor-not-allowed" : ""
                                }`}
                                value={formatPrice(
                                  row.prices[day.id as keyof typeof row.prices]
                                )}
                                onChange={(value) => {
                                  const validatedValue =
                                    validateNumericInput(value);
                                  handlePriceChange(
                                    row.roomConfigId,
                                    row.occupancyTypeId,
                                    row.mealPlanId,
                                    row.seasonalPeriodId,
                                    day.id,
                                    parsePrice(validatedValue)
                                  );
                                }}
                                onKeyDown={(e) => handleNumericKeyPress(e)}
                                debounceMs={300}
                              />
                            )}
                          </div>
                        </td>
                      </React.Fragment>
                    ))}
                  </>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Apply to All Days Confirmation Prompt */}
      <Prompt
        open={isApplyToAllDaysPromptOpen}
        onOpenChange={setIsApplyToAllDaysPromptOpen}
      >
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Apply Default Values to All Days</Prompt.Title>
            <Prompt.Description>
              Some weekdays already have cost/margin values set. This will
              override all existing weekday values with the default values:
              {pendingApplyToAllDaysData && (
                <div className="mt-3 p-3 bg-muted rounded-md">
                  <div>
                    <strong>Gross Cost:</strong>{" "}
                    {
                      pendingApplyToAllDaysData.pricingRow.defaultValues
                        .grossCost
                    }
                  </div>
                  <div>
                    <strong>Fixed Margin:</strong>{" "}
                    {
                      pendingApplyToAllDaysData.pricingRow.defaultValues
                        .fixedMargin
                    }
                  </div>
                  <div>
                    <strong>Margin %:</strong>{" "}
                    {
                      pendingApplyToAllDaysData.pricingRow.defaultValues
                        .marginPercentage
                    }
                    %
                  </div>
                </div>
              )}
              <div className="mt-3 text-sm text-muted-foreground">
                Do you want to proceed?
              </div>
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel onClick={handleCancelApplyToAllDays}>
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={handleConfirmApplyToAllDays}>
              Apply to All Days
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>

      {/* Export Modal */}
      <PricingExportModal
        open={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        hotelId={_hotelId}
        currentCurrency={currencyCode}
        roomConfigs={roomConfigs}
        occupancyConfigs={occupancyConfigs}
        mealPlans={mealPlans}
        seasonalPeriods={seasonalPeriods}
      />

      {/* Bulk Import Modal */}
      <BulkImportModal
        open={isBulkImportModalOpen}
        onClose={() => setIsBulkImportModalOpen(false)}
        hotelId={_hotelId}
        currentCurrency={currencyCode}
        roomConfigs={roomConfigs}
        occupancyConfigs={occupancyConfigs}
        mealPlans={mealPlans}
        seasonalPeriods={seasonalPeriods}
        onImportComplete={() => {
          // Refresh pricing data after successful import
          if (onRefetch) {
            onRefetch();
          } else {
            // Fallback to local data refresh if no refetch function provided
            initializeData();
          }
          setIsBulkImportModalOpen(false);
        }}
      />
    </Container>
  );
};

export default ComprehensivePricingTable;
