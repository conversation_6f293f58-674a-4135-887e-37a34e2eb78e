import {
  loadEnv,
  defineConfig,
  ContainerReg<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Mo<PERSON><PERSON>,
} from "@camped-ai/framework/utils";

loadEnv(process.env.NODE_ENV || "development", process.cwd());

module.exports = defineConfig({
  admin: {
    vite: () => {
      return {
        optimizeDeps: {
          include: ["@emotion/react", "@mui/material", "recharts"],
        },
        server: {
          allowedHosts: [".centralindia.azurecontainer.io", ".flinkk.io"],
        },
      };
    },
  },
  projectConfig: {
    databaseUrl: process.env.DATABASE_URL,
    // Redis URL for session storage - prevents token invalidation on deployment
    redisUrl: process.env.REDIS_URL,
    // databaseDriverOptions: {
    //   pool: {
    //     max: 50,
    //     min: 0,
    //     idleTimeoutMillis: 10000,
    //   }
    // },
    http: {
      storeCors: process.env.STORE_CORS!,
      adminCors: process.env.ADMIN_CORS!,
      authCors: process.env.AUTH_CORS!,
      jwtSecret: process.env.JWT_SECRET || "supersecret",
      cookieSecret: process.env.COOKIE_SECRET || "supersecret",
      bodyParser: {
        limit: "10mb", // Increase body parser limit for large imports
      },
    },
  },

  modules: [
    // Core Medusa modules - required for basic functionality
    {
      resolve: "@camped-ai/medusa/product",
    },
    {
      resolve: "@camped-ai/medusa/pricing",
    },
    {
      resolve: "@camped-ai/medusa/inventory",
    },
    {
      resolve: "@camped-ai/medusa/stock-location",
    },
    {
      resolve: "@camped-ai/medusa/fulfillment",
    },
    {
      resolve: "@camped-ai/medusa/sales-channel",
    },
    {
      resolve: "@camped-ai/medusa/cart",
    },
    {
      resolve: "@camped-ai/medusa/order",
    },
    {
      resolve: "@camped-ai/medusa/customer",
    },
    {
      resolve: "@camped-ai/medusa/region",
    },
    {
      resolve: "@camped-ai/medusa/currency",
    },
    {
      resolve: "@camped-ai/medusa/tax",
    },
    {
      resolve: "@camped-ai/medusa/promotion",
    },
    {
      resolve: "@camped-ai/medusa/api-key",
    },
    {
      resolve: "@camped-ai/medusa/store",
    },
    {
      resolve: "@camped-ai/medusa/user",
      options: {
        jwt_secret: process.env.JWT_SECRET || "supersecret",
      },
    },
    {
      resolve: "@camped-ai/medusa/workflow-engine-inmemory",
    },
    // {
    //   resolve: "@camped-ai/medusa/cache-redis",
    //   options: {
    //     redisUrl: process.env.REDIS_URL,
    //     ttl: 10 * 60,
    //   },
    // },
    {
      resolve: "@camped-ai/medusa/event-bus-local",
    },
    {
      resolve: "./src/modules/subscription",
    },
    {
      resolve: "@camped-ai/medusa/auth",
      options: {
        providers: [
          // ... other providers (or you can use otp only)
          {
            resolve: "@camped-ai/medusa/auth-emailpass",
            id: "emailpass",
            options: {
              // options...
            },
          },
          {
            resolve: "./src/modules/auth-otp",
            id: "otp",
            dependencies: [Modules.CACHE, ContainerRegistrationKeys.LOGGER],
            options: {
              // Number of digits in the OTP code (default: 6)
              digits: 6,
              // Time-to-live for OTP codes in seconds (default: 60 * 5 -> 5 minutes)
              ttl: 60 * 5,
              // Maximum number of OTP emails allowed within the rate limit window (default: 3)
              maxOtpAttempts: 3,
              // Time window in seconds for rate limiting OTP emails (default: 60 * 5 -> 5 minutes)
              rateLimitWindow: 60 * 5,
            },
          },
        ],
      },
    },
    {
      resolve: "@camped-ai/medusa/payment",
      options: {
        providers: [
          {
            resolve: "./src/modules/my-payment",
            id: "razorpay",
            options: {
              key_id: process.env.RAZORPAY_ID,
              key_secret: process.env.RAZORPAY_SECRET,
              razorpay_account: process.env.RAZORPAY_ACCOUNT,
              automatic_expiry_period: 30 /*any value between 12 minutes and 30 days expressed in minutes*/,
              manual_expiry_period: 7200,
              refund_speed: "normal",
              // webhook_secret: process.env.RAZORPAY_WEBHOOK_SECRET,
              auto_capture: true, // if you want to automatically capture,
            },
          },
          {
            resolve: "@camped-ai/medusa/payment-stripe",
            id: "stripe",
            options: {
              apiKey: process.env.STRIPE_API_KEY,
              webhookSecret: process.env.STRIPE_WEBHOOK_SECRET_KEY,
              capture: true,
              automatic_payment_methods: true,
            },
          },
        ],
      },
    },
    {
      resolve: "./src/modules/lookup",
    },
    {
      resolve: "./src/modules/rbac",
    },
    {
      resolve: "./src/modules/hotel-management/room-inventory",
    },
    {
      resolve: "./src/modules/hotel-management/hotel-pricing",
    },
    {
      resolve: "./src/modules/hotel-management/destination",
    },
    {
      resolve: "./src/modules/hotel-management/campaign-extension",
    },
    {
      resolve: "./src/modules/hotel-management/hotel",
    },
    {
      resolve: "./src/modules/hotel-management/housekeeping",
    },
    {
      resolve: "./src/modules/hotel-management/cancellation-policy",
    },
    {
      resolve: "./src/modules/hotel-management/add-on-service",
    },
    {
      resolve: "./src/modules/vendor_management",
    },
    {
      resolve: "./src/modules/supplier-products-services",
    },
    {
      resolve: "./src/modules/booking-add-ons",
    },
    // Booking-core module has been removed as we've moved to order-based functionality
    {
      resolve: "./src/modules/store-analytics",
    },
    {
      resolve: "./src/modules/store-detail",
    },
    {
      resolve: "./src/modules/metafield",
    },
    {
      resolve: "./src/modules/metafield-definition",
    },
    {
      resolve: "./src/modules/field-validation",
    },
    {
      resolve: "./src/modules/notification-template",
    },
    {
      resolve: "./src/modules/stripe-payment-link",
    },
    {
      resolve: "./src/modules/stripe-checkout",
    },
    {
      resolve: "./src/modules/whatsapp-message",
    },
    {
      resolve: "./src/modules/customer-travellers",
    },
    {
      resolve: "./src/modules/concierge-management",
    },
    {
      resolve: "./src/modules/concierge-management/itinerary",
    },
    {
      resolve: "@camped-ai/medusa/file",
      options: {
        providers: [
          {
            resolve: "@camped-ai/medusa/file-s3",
            id: "s3",
            options: {
              file_url: process.env.S3_FILE_URL,
              access_key_id: process.env.S3_ACCESS_KEY_ID,
              secret_access_key: process.env.S3_SECRET_ACCESS_KEY,
              region: process.env.S3_REGION,
              bucket: process.env.S3_BUCKET,
              endpoint: process.env.S3_ENDPOINT,
              prefix: process.env.PROJECT_NAME + "/",
              // other options...
            },
          },
        ],
      },
    },
    {
      resolve: "@camped-ai/medusa/notification",
      options: {
        providers: [
          {
            resolve: "./src/modules/my-notification",
            id: "my-notification",
            options: {
              channels: ["email"],
              from: "<EMAIL>",
            },
          },
          {
            resolve: "@camped-ai/medusa/notification-local",
            id: "local",
            options: {
              channels: ["feed"],
            },
          },
          {
            resolve: "./src/modules/whatsapp-notification",
            id: "direct-whatsapp",
            options: {
              channels: ["whatsapp"],
              from: process.env.WHATSAPP_PHONE_NUMBER_ID,
            },
          },
        ],
      },
    },
  ],
  plugins: [
    {
      resolve: "@incresco/medusa-plugin-wishlist",
      options: {},
    },
  ],
});
